"use client"

import type React from "react"

import { useState, useRef } from "react"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardHeader } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MoreHorizontal, Lock, Globe, Calendar, BookmarkIcon, GripVertical, Loader2, FolderOpen } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from "@/components/ui/dropdown-menu"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useToast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"

interface Category {
  id: string
  name: string
}

interface CollectionCardProps {
  id: string | number
  name: string
  description?: string
  coverImage?: string
  itemCount: number
  isPublic: boolean
  createdAt: string
  updatedAt: string
  category?: string
  categoryId?: string
  // 新增的 props
  categories?: Category[]
  onCategoryChange?: (collectionId: string, categoryId: string | null) => Promise<void>
}

export function CollectionCard({
  id,
  name,
  description,
  coverImage,
  itemCount,
  isPublic,
  createdAt,
  updatedAt,
  category,
  categoryId,
  categories = [],
  onCategoryChange,
}: CollectionCardProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [isUpdatingCategory, setIsUpdatingCategory] = useState(false)
  const router = useRouter()
  const { toast } = useToast()
  const cardRef = useRef<HTMLDivElement>(null)

  // 處理收藏牆拖動開始
  const handleDragStart = (e: React.DragEvent) => {
    if (!cardRef.current) return

    // 設置拖拽數據 - 收藏牆拖拽
    const dragData = JSON.stringify({
      collectionId: id.toString(),
      collectionName: name,
      type: "collection",
      currentCategoryId: categoryId,
    })

    e.dataTransfer.setData("application/json", dragData)
    e.dataTransfer.setData("text/collection-drag", "true") // 添加收藏牆拖拽標識
    e.dataTransfer.effectAllowed = "move"

    setIsDragging(true)

    // 添加全局拖拽樣式並存儲分類信息
    document.body.classList.add("dragging-collection")
    cardRef.current.classList.add("dragging-collection")
    cardRef.current.setAttribute("data-category-id", categoryId || "")
  }

  // 處理收藏牆拖動結束
  const handleDragEnd = () => {
    setIsDragging(false)

    // 移除全局拖拽樣式
    document.body.classList.remove("dragging-collection")
    if (cardRef.current) {
      cardRef.current.classList.remove("dragging-collection")
      cardRef.current.removeAttribute("data-category-id")
    }
  }

  // 格式化日期
  const formattedDate = new Date(updatedAt).toLocaleDateString("zh-TW", {
    year: "numeric",
    month: "short",
    day: "numeric",
  })

  // CollectionCard 不處理拖拽進入，因為卡片拖拽到收藏牆由側邊欄處理
  // 這裡只處理收藏牆自身的拖拽





  // 處理分類變更
  const handleCategoryChange = async (newCategoryId: string | null) => {
    if (!onCategoryChange) return

    try {
      setIsUpdatingCategory(true)
      await onCategoryChange(id.toString(), newCategoryId)
    } catch (error) {
      console.error("變更分類時出錯:", error)
      toast({
        title: "變更失敗",
        description: "請稍後再試",
        variant: "destructive",
      })
    } finally {
      setIsUpdatingCategory(false)
    }
  }

  // 處理卡片點擊
  const handleCardClick = () => {
    router.push(`/library/collection/${id}`)
  }

  return (
    <Card
      ref={cardRef}
      className={cn(
        "overflow-hidden transition-all cursor-pointer group",
        isDragging && "opacity-50 scale-95",
        "cursor-grab active:cursor-grabbing"
      )}
      onClick={handleCardClick}
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div
        className="h-32 bg-cover bg-center"
        style={{ backgroundImage: `url(${coverImage || "/abstract-geometric-shapes.png"})` }}
      >
        <div className="flex justify-between items-start p-3 bg-gradient-to-b from-black/60 to-transparent">
          <Badge variant={isPublic ? "default" : "secondary"} className="flex items-center gap-1">
            {isPublic ? <Globe className="h-3 w-3" /> : <Lock className="h-3 w-3" />}
            {isPublic ? "公開" : "私人"}
          </Badge>

          <div className="flex items-center gap-1">
            {/* 拖拽手柄 */}
            <div className="opacity-0 group-hover:opacity-100 transition-opacity">
              <GripVertical className="h-4 w-4 text-white/70" />
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 bg-black/20 text-white hover:bg-black/40"
                  onClick={(e) => e.stopPropagation()} // 防止觸發卡片點擊
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" onClick={(e) => e.stopPropagation()}>
                <DropdownMenuItem asChild>
                  <Link href={`/library/collection/${id}`}>查看詳情</Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={`/library/collection/${id}/edit`}>編輯收藏牆</Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />

                {/* 分類管理選項 */}
                <DropdownMenuLabel className="flex items-center gap-2">
                  <FolderOpen className="h-4 w-4" />
                  移動到分類
                  {isUpdatingCategory && <Loader2 className="h-3 w-3 animate-spin" />}
                </DropdownMenuLabel>

                {/* 移動到未分類 */}
                <DropdownMenuItem
                  onClick={() => handleCategoryChange(null)}
                  disabled={isUpdatingCategory || !categoryId}
                  className="pl-6"
                >
                  未分類
                  {!categoryId && <span className="ml-auto text-xs">✓</span>}
                </DropdownMenuItem>

                {/* 現有分類選項 */}
                {categories.map((cat) => (
                  <DropdownMenuItem
                    key={cat.id}
                    onClick={() => handleCategoryChange(cat.id)}
                    disabled={isUpdatingCategory || categoryId === cat.id}
                    className="pl-6"
                  >
                    {cat.name}
                    {categoryId === cat.id && <span className="ml-auto text-xs">✓</span>}
                  </DropdownMenuItem>
                ))}

                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-destructive">刪除收藏牆</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
      <CardHeader className="p-4 pb-0">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="font-semibold text-lg line-clamp-1">{name}</h3>
            <p className="text-sm text-muted-foreground line-clamp-1">{category}</p>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4 pt-2">
        {description && <p className="text-sm text-muted-foreground line-clamp-2 mb-2">{description}</p>}
      </CardContent>
      <CardFooter className="p-4 pt-0 flex justify-between items-center text-sm text-muted-foreground">
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4" />
          <span>{formattedDate}</span>
        </div>
        <div className="flex items-center gap-2">
          <BookmarkIcon className="h-4 w-4" />
          <span>{itemCount} 項</span>
        </div>
      </CardFooter>
    </Card>
  )
}
